"use client";
import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import { useTimeSettingsStore } from "@/store/timeSettingsStore";
import socketService from "@/services/socketService";
import { Clock } from "lucide-react";

interface PlayTimeframeVotingProps {
  onVotingComplete: () => void;
}

type TimeOption = 3 | 4 | 5 | 6 | 60;
type VoteResults = Record<TimeOption, number>;

export default function PlayTimeframeVoting({
  onVotingComplete,
}: PlayTimeframeVotingProps) {
  const { players } = useGameStore();
  const { settings } = useTimeSettingsStore();
  const [selectedOption, setSelectedOption] = useState<TimeOption | null>(null);
  const [hasVoted, setHasVoted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Server-provided timeframe options (will be set when received from server)
  const [serverTimeOptions, setServerTimeOptions] = useState<TimeOption[]>([]);
  const [serverVotingTimeLimit, setServerVotingTimeLimit] = useState<number>(15);

  // Use server options if available, otherwise fall back to local settings
  const timeOptions: TimeOption[] = serverTimeOptions.length > 0 ? serverTimeOptions : settings.playTimeframeOptions as TimeOption[];
  const votingTimeLimit = serverTimeOptions.length > 0 ? serverVotingTimeLimit : settings.votingTimeLimit;

  // Initialize vote results based on available options
  const [voteResults, setVoteResults] = useState<VoteResults>(() => {
    const results: VoteResults = {} as VoteResults;
    timeOptions.forEach(option => {
      results[option as TimeOption] = 0;
    });
    return results;
  });

  const [votingTimeLeft, setVotingTimeLeft] = useState(votingTimeLimit);
  const [playersVoted, setPlayersVoted] = useState<string[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState<TimeOption | null>(null);
  const [showResults, setShowResults] = useState(false);

  // Handle option selection
  const handleSelectOption = (option: TimeOption) => {
    if (hasVoted || isSubmitting) return;
    setSelectedOption(option);
  };

  // Handle vote submission
  const handleSubmitVote = useCallback(async () => {
    if (!selectedOption || hasVoted || isSubmitting) return;

    try {
      setIsSubmitting(true);
      await socketService.sendGameAction("vote_timeframe", {
        timeframe: selectedOption,
      });
      setHasVoted(true);
      setIsSubmitting(false);
    } catch (error) {
      console.error("Error submitting vote:", error);
      setIsSubmitting(false);
    }
  }, [selectedOption, hasVoted, isSubmitting]);

  // Listen for server-provided timeframe options
  useEffect(() => {
    const handleTimeframeOptions = (data: {
      timeOptions: TimeOption[];
      votingTimeLimit: number;
    }) => {
      console.log('Received timeframe options from server:', data);
      setServerTimeOptions(data.timeOptions);
      setServerVotingTimeLimit(data.votingTimeLimit);
      setVotingTimeLeft(data.votingTimeLimit);

      // Update vote results based on new options
      const results: VoteResults = {} as VoteResults;
      data.timeOptions.forEach(option => {
        results[option as TimeOption] = 0;
      });
      setVoteResults(results);
    };

    socketService.on("timeframe_options", handleTimeframeOptions);

    return () => {
      socketService.off("timeframe_options", handleTimeframeOptions);
    };
  }, []);

  // Listen for voting events from the server
  useEffect(() => {
    const handleVoteReceived = (data: {
      playerId: string;
      playerName: string;
    }) => {
      // Add player to the list of players who have voted
      setPlayersVoted((prev) => [...prev, data.playerId]);
    };

    const handleVoteResults = (data: {
      results: VoteResults;
      selectedTimeframe: TimeOption;
    }) => {
      setVoteResults(data.results);
      setSelectedTimeframe(data.selectedTimeframe);
      setShowResults(true);

      // After showing results for 3 seconds, complete the voting process
      setTimeout(() => {
        onVotingComplete();
      }, 3000);
    };

    socketService.on("timeframe_vote_received", handleVoteReceived);
    socketService.on("timeframe_vote_results", handleVoteResults);

    return () => {
      socketService.off("timeframe_vote_received", handleVoteReceived);
      socketService.off("timeframe_vote_results", handleVoteResults);
    };
  }, [onVotingComplete]);

  // Update voting time left when voting time limit changes
  useEffect(() => {
    setVotingTimeLeft(votingTimeLimit);
  }, [votingTimeLimit]);

  // Countdown timer for voting
  useEffect(() => {
    if (hasVoted || showResults) return;

    const timer = setInterval(() => {
      setVotingTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // If time runs out and player hasn't voted, auto-submit with default option
          if (!hasVoted && !isSubmitting && timeOptions.length > 0) {
            setSelectedOption(timeOptions[0]); // Select first available option
            handleSubmitVote();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [hasVoted, isSubmitting, showResults, timeOptions, handleSubmitVote]);

  // Check if all players have voted
  useEffect(() => {
    if (playersVoted.length === players.length && players.length > 0) {
      // All players have voted, server will send results
      console.log("All players have voted");
    }
  }, [playersVoted, players]);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/80"
      >
        <div className="bg-gray-900 border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full mx-4">
          {!showResults ? (
            <>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-[#E1C760] text-xl font-bold">
                  Vote for Play Timeframe
                </h2>
                <div className="bg-black/70 text-[#E1C760] px-2 py-1 rounded-md text-sm">
                  {votingTimeLeft}s
                </div>
              </div>

              <p className="text-white text-sm mb-4">
                How many seconds should each player have to play a card? If a player doesn't play within the timeframe, the opposite team wins a ball.
              </p>

              <div className="grid grid-cols-2 gap-3 mb-6">
                {timeOptions.map((option) => (
                  <button
                    key={option}
                    onClick={() => handleSelectOption(option)}
                    disabled={hasVoted || isSubmitting}
                    className={`p-4 rounded-lg flex flex-col items-center justify-center ${
                      selectedOption === option
                        ? "bg-[#E1C760] text-black"
                        : "bg-gray-800 text-white hover:bg-gray-700"
                    } ${
                      hasVoted || isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <Clock className="mb-2" size={24} />
                    <span className="text-2xl font-bold">{option === 60 ? "1" : option}</span>
                    <span className="text-xs">{option === 60 ? "minute" : "seconds"}</span>
                  </button>
                ))}
              </div>

              <div className="flex justify-center">
                <button
                  onClick={handleSubmitVote}
                  disabled={!selectedOption || hasVoted || isSubmitting}
                  className={`w-full py-3 rounded-lg font-bold ${
                    selectedOption && !hasVoted && !isSubmitting
                      ? "bg-[#E1C760] text-black"
                      : "bg-gray-700 text-gray-400 cursor-not-allowed"
                  }`}
                >
                  {isSubmitting
                    ? "Submitting..."
                    : hasVoted
                    ? "Vote Submitted"
                    : "Submit Vote"}
                </button>
              </div>

              {hasVoted && (
                <div className="mt-4 text-center text-white text-sm">
                  Waiting for other players to vote...
                  <div className="mt-2 flex justify-center">
                    <div className="w-8 h-8 border-3 border-[#E1C760] border-t-transparent rounded-full animate-spin"></div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <>
              <h2 className="text-[#E1C760] text-xl font-bold mb-4 text-center">
                Voting Results
              </h2>

              <div className="grid grid-cols-2 gap-3 mb-6">
                {timeOptions.map((option) => (
                  <div
                    key={option}
                    className={`p-4 rounded-lg flex flex-col items-center justify-center ${
                      selectedTimeframe === option
                        ? "bg-[#E1C760] text-black"
                        : "bg-gray-800 text-white"
                    }`}
                  >
                    <Clock className="mb-2" size={24} />
                    <span className="text-2xl font-bold">{option === 60 ? "1" : option}</span>
                    <span className="text-xs">{option === 60 ? "minute" : "seconds"}</span>
                    <div className="mt-2 text-sm">
                      {voteResults[option]} vote{voteResults[option] !== 1 ? "s" : ""}
                    </div>
                  </div>
                ))}
              </div>

              <div className="text-center text-white">
                <p className="mb-2">
                  Selected timeframe: <span className="text-[#E1C760] font-bold">
                    {selectedTimeframe === 60 ? "1 minute" : `${selectedTimeframe} seconds`}
                  </span>
                </p>
                <p className="text-sm text-gray-400">
                  Game will start in a moment...
                </p>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
