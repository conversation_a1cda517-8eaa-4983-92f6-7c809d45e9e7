"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ShuffleType } from "./ShuffleOptions";

interface CardShuffleAnimationProps {
  isVisible: boolean;
  onComplete: () => void;
  duration?: number; // Duration in seconds
  shuffleType: ShuffleType;
}

export default function CardShuffleAnimation({
  isVisible,
  onComplete,
  duration = 5,
  shuffleType = "cascade",
}: CardShuffleAnimationProps) {
  const [isAnimating, setIsAnimating] = useState(true);
  const [progress, setProgress] = useState(0);
  const [phase, setPhase] = useState<"initial" | "middle" | "final">("initial");

  // Start the animation when the component becomes visible
  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true);
      setProgress(0);
      setPhase("initial");

      // Start progress timer
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + (100 / (duration * 10));
          if (newProgress >= 33 && prev < 33) {
            setPhase("middle");
          } else if (newProgress >= 66 && prev < 66) {
            setPhase("final");
          }
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 100);

      // Complete animation after duration
      const timer = setTimeout(() => {
        setIsAnimating(false);
        setTimeout(() => {
          onComplete();
        }, 500);
      }, duration * 1000);

      return () => {
        clearInterval(interval);
        clearTimeout(timer);
      };
    }
  }, [isVisible, duration, onComplete]);

  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center">
      <h2 className="text-2xl font-bold text-[#E1C760] mb-4">
        {shuffleType === "cascade" ? "Cascade Shuffle" :
         shuffleType === "riffle" ? "Riffle Shuffle" :
         "Overhand Shuffle"}
      </h2>

      <div className="relative h-60 w-60 mb-8">
        {shuffleType === "cascade" && (
          <>
            {/* Cascade Shuffle Animation */}
            <AnimatePresence>
              {isAnimating && Array.from({ length: 24 }).map((_, index) => (
                <motion.div
                  key={`cascade-${index}`}
                  className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                  style={{
                    backgroundImage: "url('/assets/CardBack/card-back.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    left: "calc(50% - 40px)",
                    top: "calc(50% - 56px)",
                  }}
                  initial={{
                    x: 0,
                    y: 0,
                    rotate: 0,
                    scale: 0.9 + (index * 0.005)
                  }}
                  animate={
                    phase === "initial" ? {
                      x: [0, Math.random() * 40 - 20, 0],
                      y: [0, Math.random() * 40 - 20, 0],
                      rotate: [0, Math.random() * 10 - 5, 0],
                      scale: 0.9 + (index * 0.005)
                    } :
                    phase === "middle" ? {
                      x: index < 12 ? -50 : 50,
                      y: (index % 12) * 2,
                      rotate: Math.random() * 5 - 2.5,
                      zIndex: index
                    } : {
                      x: 0,
                      y: 0,
                      rotate: 0,
                      zIndex: index,
                      scale: 0.9 + (index * 0.005)
                    }
                  }
                  transition={{
                    duration: 0.5,
                    repeat: phase === "initial" ? Infinity : 0,
                    repeatType: "loop",
                    delay: phase === "initial" ? index * 0.02 : 0,
                  }}
                />
              ))}
            </AnimatePresence>
          </>
        )}

        {shuffleType === "riffle" && (
          <>
            {/* Riffle Shuffle Animation */}
            {phase === "initial" && (
              <>
                {/* Left pile */}
                <AnimatePresence>
                  {isAnimating && Array.from({ length: 12 }).map((_, index) => (
                    <motion.div
                      key={`riffle-left-${index}`}
                      className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                      style={{
                        backgroundImage: "url('/assets/CardBack/GR2.svg')",
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        left: "calc(30% - 40px)",
                        top: "calc(50% - 56px)",
                      }}
                      initial={{
                        x: 0,
                        y: 0,
                        rotate: 0,
                        scale: 0.9 + (index * 0.005)
                      }}
                      animate={{
                        x: [0, Math.random() * 10 - 5, 0],
                        y: [0, Math.random() * 10 - 5, 0],
                        rotate: [0, Math.random() * 6 - 3, 0],
                        scale: 0.9 + (index * 0.005),
                      }}
                      transition={{
                        duration: 0.5,
                        repeat: isAnimating ? Infinity : 0,
                        repeatType: "loop",
                        delay: index * 0.02,
                      }}
                    />
                  ))}
                </AnimatePresence>

                {/* Right pile */}
                <AnimatePresence>
                  {isAnimating && Array.from({ length: 12 }).map((_, index) => (
                    <motion.div
                      key={`riffle-right-${index}`}
                      className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                      style={{
                        backgroundImage: "url('/assets/CardBack/GR2.svg')",
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        left: "calc(70% - 40px)",
                        top: "calc(50% - 56px)",
                      }}
                      initial={{
                        x: 0,
                        y: 0,
                        rotate: 0,
                        scale: 0.9 + (index * 0.005)
                      }}
                      animate={{
                        x: [0, Math.random() * 10 - 5, 0],
                        y: [0, Math.random() * 10 - 5, 0],
                        rotate: [0, Math.random() * 6 - 3, 0],
                        scale: 0.9 + (index * 0.005),
                      }}
                      transition={{
                        duration: 0.5,
                        repeat: isAnimating ? Infinity : 0,
                        repeatType: "loop",
                        delay: index * 0.02,
                      }}
                    />
                  ))}
                </AnimatePresence>
              </>
            )}

            {phase === "middle" && (
              <>
                {/* Thumb positions for riffle */}
                <AnimatePresence>
                  {Array.from({ length: 24 }).map((_, index) => (
                    <motion.div
                      key={`riffle-thumb-${index}`}
                      className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                      style={{
                        backgroundImage: "url('/assets/CardBack/GR2.svg')",
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        left: "calc(50% - 40px)",
                        top: "calc(50% - 56px)",
                      }}
                      initial={{
                        x: index < 12 ? -30 : 30,
                        y: 10,
                        rotate: index < 12 ? -15 : 15,
                        zIndex: index < 12 ? index * 2 : (index - 12) * 2 + 1
                      }}
                      animate={{
                        x: 0,
                        y: 40 + (index % 12),
                        rotate: Math.random() * 4 - 2,
                        zIndex: 100 + index
                      }}
                      transition={{
                        duration: 0.3,
                        delay: (index % 12) * 0.1,
                      }}
                    />
                  ))}
                </AnimatePresence>
              </>
            )}

            {phase === "final" && (
              <AnimatePresence>
                {Array.from({ length: 24 }).map((_, index) => (
                  <motion.div
                    key={`riffle-final-${index}`}
                    className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                    style={{
                      backgroundImage: "url('/assets/CardBack/GR2.svg')",
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      left: "calc(50% - 40px)",
                      top: "calc(50% - 56px)",
                      zIndex: index,
                    }}
                    initial={{
                      x: 0,
                      y: 40 + (index % 12),
                      rotate: Math.random() * 4 - 2,
                    }}
                    animate={{
                      x: 0,
                      y: 0,
                      rotate: 0,
                      scale: 0.9 + (index * 0.005)
                    }}
                    transition={{
                      duration: 0.5,
                      delay: index * 0.02,
                    }}
                  />
                ))}
              </AnimatePresence>
            )}
          </>
        )}

        {shuffleType === "overhand" && (
          <>
            {/* Overhand Shuffle Animation */}
            <AnimatePresence>
              {isAnimating && Array.from({ length: 24 }).map((_, index) => {
                // Calculate which group this card belongs to (5 groups of ~5 cards each)
                const group = Math.floor(index / 5);
                const isActive = phase === "middle" && Math.floor(progress / 10) % 5 === group;

                return (
                  <motion.div
                    key={`overhand-${index}`}
                    className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                    style={{
                      backgroundImage: "url('/assets/CardBack/GR2.svg')",
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      left: "calc(50% - 40px)",
                      top: "calc(50% - 56px)",
                    }}
                    initial={{
                      x: 0,
                      y: index * 0.5,
                      rotate: 0,
                      scale: 0.9 + (index * 0.005),
                      zIndex: index
                    }}
                    animate={
                      phase === "initial" ? {
                        x: 0,
                        y: index * 0.5,
                        rotate: 0,
                        scale: 0.9 + (index * 0.005),
                        zIndex: index
                      } :
                      phase === "middle" && isActive ? {
                        x: 40,
                        y: -40,
                        rotate: Math.random() * 4 - 2,
                        zIndex: 100 + index,
                        scale: 1.1
                      } :
                      phase === "final" ? {
                        x: 0,
                        y: 0,
                        rotate: 0,
                        zIndex: 24 - index,
                        scale: 0.9 + ((24 - index) * 0.005)
                      } : {}
                    }
                    transition={{
                      duration: 0.4,
                      delay: isActive ? 0 : 0.02 * index,
                    }}
                  />
                );
              })}
            </AnimatePresence>
          </>
        )}
      </div>

      {/* Status message */}
      <div className="text-center text-white text-lg mt-4">
        {shuffleType === "cascade" ? (
          progress < 33 ? "Spreading cards..." :
          progress < 66 ? "Cascading cards..." :
          progress < 90 ? "Completing cascade..." :
          "Shuffle complete!"
        ) : shuffleType === "riffle" ? (
          progress < 33 ? "Splitting the deck..." :
          progress < 66 ? "Riffling cards together..." :
          progress < 90 ? "Completing the bridge..." :
          "Shuffle complete!"
        ) : (
          progress < 33 ? "Preparing overhand shuffle..." :
          progress < 66 ? "Transferring card packets..." :
          progress < 90 ? "Completing the shuffle..." :
          "Shuffle complete!"
        )}
      </div>

      {/* Progress bar */}
      <div className="w-64 h-2 bg-gray-800 rounded-full mt-4 overflow-hidden">
        <motion.div
          className="h-full bg-[#E1C760]"
          initial={{ width: "0%" }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  );
}
